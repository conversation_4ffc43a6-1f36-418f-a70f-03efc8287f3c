<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, DataLine, InfoFilled, Monitor, RefreshLeft, Setting, User } from '@element-plus/icons-vue'
import { GetFineCodeDetail, WeighingFineCode } from '@/api/clothTicket'
import { useMeasureWeightStore } from '@/stores/measureWeight'
import { useMeasureWeightService } from '@/use/useMeasureWeightService'
import { Carry, CarryLabels, DecimalPoint, DecimalPointLabels, EmployeeType, GlobalEnum } from '@/common/enum'
import SvgIcon from '@/components/Svglcon/index.vue'
import { formatHashTag, formatWeightDiv, formatWeightMul } from '@/common/format'
import SelectMergeComponent from '@/components/SelectMergeComponent/index.vue'
import ElectronicScaleSettings from '@/components/ElectronicScaleSettings/index.vue'
import { BatchUpdateGlobalConfigList, GetGlobalConfigDropdownList, createGlobalConfig, updateGlobalConfig } from '@/api/globalConfig'
import { getProductionNotifyOrder } from '@/api/productionNotice'
import { Business_unitcustomerDetail } from '@/api/customerMange'

// 挂载快捷键监听

// 表单数据
const formData = reactive({
  // 生产信息录入
  barcode: '', // 条码编号
  machineNumber: '', // 机台号
  weaverId: null, // 织工ID
  weaverName: '', // 织工ID
  isShiftWorker: false, // 是否为交班织工
  shiftDuration: '', // 交班时长(rpm)
  shiftWeaverId: null, // 交班织工ID
  shiftWeaverName: '', // 交班织工姓名
  electronicWeight: 0, // 电子秤重量(kg)
  actualWeight: 0, // 实际重量(kg)
  fineCodeId: null as number | null, // 细码ID
})

// 生产参数详情数据
const productionDetails = reactive({
  orderName: '', // 订单名称
  orderNumber: '', // 编号
  version: '', // 版号
  versionNumber: '', // 卷号
  batch: '', // 纱批
  machineType: '', // 织机类型
  machineColor: '', // 原料颜色
  needleSize: '', // 针寸数
  group: '', // 班次
  machine: '', // 机台
  isShiftWorker: '', // 是否交班布
  shiftTime: 0, // 称重
  shiftDuration: '', // 交班转速
  shiftWeaverName: '', // 交班织工
  actualWeight: 0, // 实重
  operator: '', // 织工
  monthlyOutput: '', // 本月产量
  yearlyOutput: '', // 本月产量
  productionNote: '', // 生产备注
  weightOfFabric: 0, // 布匹定重
})

// 加载状态
const loading = ref(false)

// 表单引用
const formRef = ref()

// 自动保存状态
const autoSave = ref(false)

// 电子秤相关
const measureWeightStore = useMeasureWeightStore()
// 弹窗控制
const showElectronicScaleModal = ref(false)
const showWeightLog = ref(false)

// 电子秤设置
const electronicScaleSettings = reactive({
  connection: '链接',
  stableValue: 100,
  isStable: false,
  autoSave: true,
  autoSaveSeconds: 5,
  noWeaverSelection: false,
  noInspectorSelection: false,
  weightReflection: false,
  dataHead: '',
  dataEnd: '',
  scanCodeReading: false,
  portAutoOpen: false,
})

// 小数位数和进位方式配置
const weightPrecision = ref<DecimalPoint>(DecimalPoint.DecimalPointTwo) // 默认2位小数
const weightCarryType = ref<Carry>(Carry.CarryRoundOff) // 默认四舍五入
const { fetchData: getGlobalConfig, data: globalConfigData, success: globalConfigSuccess, msg: globalConfigMsg } = GetGlobalConfigDropdownList()

// 配置ID映射 - 使用enum
const configIdMap = {
  autoSaveSeconds: GlobalEnum.AutoSaveSeconds, // 设置延后多少秒保存
  noWeaverSelection: GlobalEnum.NoWeaverSelection, // 是否无需选择织工
  noInspectorSelection: GlobalEnum.NoInspectorSelection, // 是否无需选择查布
  weightReflection: GlobalEnum.WeightReflection, // 是否重量反转
  dataHead: GlobalEnum.DataHead, // 数据头 文本
  dataEnd: GlobalEnum.DataEnd, // 数据尾 文本
  scanCodeReading: GlobalEnum.ScanCodeReading, // 是否扫码后读数
  stableValue: GlobalEnum.StableValue, // 设置稳定值 文本
  autoSave: GlobalEnum.AutoSave, // 是否自动保存
}

// 获取设置数据
async function getSettingsData() {
  const ids = Object.values(configIdMap).join(',')
  await getGlobalConfig({
    ids,
  })

  // 将获取到的配置应用到设置中
  if (globalConfigSuccess.value)
    applyGlobalConfigToSettings()
  else
    ElMessage.error(globalConfigMsg.value)
}

// 将全局配置应用到电子秤设置
function applyGlobalConfigToSettings() {
  if (!globalConfigData.value)
    return

  globalConfigData.value?.list?.forEach((config: any) => {
    const configId = config.id
    switch (configId) {
      case configIdMap.autoSaveSeconds:
        electronicScaleSettings.autoSaveSeconds = Number(config.options) || 5
        break
      case configIdMap.noWeaverSelection:
        electronicScaleSettings.noWeaverSelection = config.options === 'true' || config.options === true
        break
      case configIdMap.noInspectorSelection:
        electronicScaleSettings.noInspectorSelection = config.options === 'true' || config.options === true
        break
      case configIdMap.weightReflection:
        electronicScaleSettings.weightReflection = config.options === 'true' || config.options === true
        break
      case configIdMap.dataHead:
        electronicScaleSettings.dataHead = config.options || ''
        break
      case configIdMap.dataEnd:
        electronicScaleSettings.dataEnd = config.options || ''
        break
      case configIdMap.scanCodeReading:
        electronicScaleSettings.scanCodeReading = config.options === 'true' || config.options === true
        break
      case configIdMap.stableValue:
        electronicScaleSettings.stableValue = Number(config.options) || 0
        electronicScaleSettings.isStable = !!Number(config.options)
        break
      case configIdMap.autoSave:
        electronicScaleSettings.autoSave = config.options === 'true' || config.options === true
        autoSave.value = electronicScaleSettings.autoSave
        break
    }
  })
  updateWeightConfig()
}
// 自动保存定时器
let autoSaveTimer: NodeJS.Timeout | null = null

// 应用进位方式
function applyCarryType(value: number, carryType: Carry, precision: number): number {
  const multiplier = 10 ** precision

  switch (carryType) {
    case Carry.CarryOne:
      // 逢1进位：小数部分有任何值就进位
      return Math.ceil(value * multiplier) / multiplier
    case Carry.CarryTwo:
      // 逢2进位：小数部分>=0.2就进位
      return Math.ceil((value - 0.1) * multiplier) / multiplier
    case Carry.CarryThree:
      // 逢3进位：小数部分>=0.3就进位
      return Math.ceil((value - 0.2) * multiplier) / multiplier
    case Carry.CarryFour:
      // 逢4进位：小数部分>=0.4就进位
      return Math.ceil((value - 0.3) * multiplier) / multiplier
    case Carry.CarryRoundOff:
    default:
      // 四舍五入
      return Math.round(value * multiplier) / multiplier
  }
}

// 重量反转函数 - 数值倒序读取
function reverseWeight(weight: number): number {
  if (weight === 0)
    return 0

  // 保存符号
  const isNegative = weight < 0
  const absWeight = Math.abs(weight)

  // 获取当前的小数位数设置
  const decimalPlaces = weightPrecision.value

  // 转换为字符串，使用动态小数位数
  const weightStr = absWeight.toFixed(decimalPlaces)

  // 移除小数点，获取所有数字
  const digitsOnly = weightStr.replace('.', '')

  // 倒序排列数字
  const reversedDigits = digitsOnly.split('').reverse().join('')

  // 重新插入小数点（保持相同的小数位数）
  const reversedWeight = Number.parseFloat(
    `${reversedDigits.slice(0, -decimalPlaces)}.${reversedDigits.slice(-decimalPlaces)}`,
  )

  // 应用进位方式
  const processedWeight = applyCarryType(reversedWeight, weightCarryType.value, decimalPlaces)

  // 恢复符号
  return isNegative ? -processedWeight : processedWeight
}

// 连接状态
const isWeightConnected = computed(() => measureWeightStore.measureWeightState?.isConnected || false)
const WeightLog = computed(() => {
  return measureWeightStore.measureWeightState.Log
})
// 监听电子秤数据
watch(() => measureWeightStore.measureWeightState.currentFrameData, (newValue: number) => {
  // 检查是否开启了"扫码后才读数"功能
  if (electronicScaleSettings.scanCodeReading) {
    // 如果开启了扫码后才读数，但没有扫码，则重量为0
    if (!formData.barcode || formData.barcode.trim() === '') {
      formData.actualWeight = 0
      return
    }
  }

  // 处理重量反转功能
  let processedWeight = newValue
  if (electronicScaleSettings.weightReflection)
    processedWeight = reverseWeight(newValue) // 重量反转：数值倒序读取

  // 更新重量
  formData.actualWeight = processedWeight
})
watch(() => measureWeightStore.measureWeightState.currentWeight, (newValue: string) => {
  // 检查是否开启了"扫码后才读数"功能
  if (electronicScaleSettings.scanCodeReading) {
    // 如果开启了扫码后才读数，但没有扫码，则重量为0
    if (!formData.barcode || formData.barcode.trim() === '') {
      formData.electronicWeight = 0
      return
    }
  }
  // 处理重量反转功能
  let processedWeight = Number(newValue)
  if (electronicScaleSettings.weightReflection)
    processedWeight = reverseWeight(Number(newValue)) // 重量反转：数值倒序读取

  // 更新重量
  formData.electronicWeight = processedWeight
})

// 同步自动保存状态
watch(() => electronicScaleSettings.autoSave, (newValue) => {
  autoSave.value = newValue
})

watch(() => autoSave.value, (newValue) => {
  electronicScaleSettings.autoSave = newValue
})

// 监听必填项数据变化，触发自动保存
watch([
  () => formData.barcode,
  () => formData.weaverId,
  () => formData.electronicWeight,
  () => formData.actualWeight,
  () => electronicScaleSettings.noWeaverSelection, // 监听织工选择设置变化
], () => {
  if (autoSave.value)
    checkAndStartAutoSave()
})

// 检查表单验证并启动自动保存
async function checkAndStartAutoSave() {
  try {
    const isValid = await canAutoSave()
    if (isValid)
      startAutoSaveTimer()
  }
  catch (error) {
    // 表单验证失败，不启动自动保存
    // 静默处理，不显示错误信息
  }
}

// 检查是否可以自动保存 - 使用表单验证
async function canAutoSave(): Promise<boolean> {
  if (!formRef.value)
    return false

  try {
    await formRef.value.validate()
    return true
  }
  catch (error) {
    return false
  }
}

// 启动自动保存定时器
function startAutoSaveTimer() {
  // 清除之前的定时器
  if (autoSaveTimer)
    clearTimeout(autoSaveTimer)

  // 设置新的定时器
  autoSaveTimer = setTimeout(async () => {
    const isValid = await canAutoSave()
    if (isValid)
      handleSave()
  }, electronicScaleSettings.autoSaveSeconds * 1000)
}
const { fetchData: fetchProductionNotifyOrder, data: productionNotifyData, success: productionNotifySuccess, msg: productionNotifyMsg } = getProductionNotifyOrder()
const { fetchData: fetchCustomerDetail, data: customerData, success: customerSuccess, msg: customerMsg } = Business_unitcustomerDetail()
const { fetchData, success, msg, data } = GetFineCodeDetail()

// 获取细码详情
async function getFineCodeDetail(barcode?: string, id?: number) {
  if (!barcode && !id) {
    ElMessage.error('请提供条码或细码ID')
    return
  }

  loading.value = true
  try {
    await fetchData({
      fabric_piece_code: barcode,
      id,
    })

    if (success.value) {
      // 更新表单数据
      formData.fineCodeId = data.value.id || null
      formData.machineNumber = data.value.machine_number || ''

      // 更新生产详情数据
      productionDetails.orderName = data.value.production_notify_order_no || ''
      productionDetails.orderNumber = data.value.fabric_piece_code || ''
      productionDetails.version = data.value.production_schedule_order_no || ''
      productionDetails.versionNumber = data.value.volume_number?.toString() || ''
      productionDetails.batch = data.value.yarn_batch || ''
      productionDetails.machineType = data.value.loom_model_name || ''
      productionDetails.machineColor = data.value.grey_fabric_color_name || ''
      productionDetails.needleSize = data.value.needle_size || ''
      productionDetails.machine = data.value.machine_number || ''
      productionDetails.actualWeight = formatWeightDiv(data.value.weighing_weight || 0)
      productionDetails.monthlyOutput = data.value.month_weighing_count ? `${data.value.month_weighing_count}条` : ''
      productionDetails.yearlyOutput = data.value.today_weighing_count ? `${data.value.today_weighing_count}条` : ''
      productionDetails.productionNote = data.value.produce_remark || ''
      productionDetails.weightOfFabric = data.value.weight_of_fabric || 0
      productionDetails.shiftTime = formatWeightDiv(data.value.electronic_scale_weight || 0)
      productionDetails.shiftDuration = data.value.shift_speed || 0
      productionDetails.shiftWeaverName = data.value.shift_weaver_name
      productionDetails.isShiftWorker = data.value.is_shift_weaver ? '是' : '否'
      await fetchProductionNotifyOrder({
        id: data.value.production_notify_order_id!,
      })
      if (!productionNotifySuccess.value)
        return ElMessage.error(productionNotifyMsg.value)
      await fetchCustomerDetail({ id: productionNotifyData.value.customer_id! })
      if (!customerSuccess.value)
        return ElMessage.error(customerMsg.value)

      const carry = customerData.value.carry as Carry
      const decimalPoint = customerData.value.decimal_point as DecimalPoint

      // 更新小数位数和进位方式配置
      if (decimalPoint !== undefined && decimalPoint !== null)
        weightPrecision.value = decimalPoint

      if (carry !== undefined && carry !== null)
        weightCarryType.value = carry
    }
    else {
      ElMessage.error(msg.value || '获取细码详情失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '获取细码详情失败')
  }
  finally {
    loading.value = false
  }
}

// 扫码输入处理
async function handleBarcodeInput() {
  if (formData.barcode) {
    await getFineCodeDetail(formData.barcode)

    // 如果开启了"扫码后才读数"功能，扫码后立即更新电子秤重量
    if (electronicScaleSettings.scanCodeReading && measureWeightStore.measureWeightState.currentFrameData) {
      let currentFrameData = measureWeightStore.measureWeightState.currentFrameData
      let currentWeight = Number(measureWeightStore.measureWeightState.currentWeight)

      // 处理重量反转功能
      if (electronicScaleSettings.weightReflection) {
        currentFrameData = reverseWeight(currentFrameData) // 重量反转：数值倒序读取
        currentWeight = reverseWeight(currentWeight) // 重量反转：数值倒序读取
      }

      formData.electronicWeight = currentWeight
      formData.actualWeight = currentFrameData
    }
  }
  else {
    // 如果清空了条码且开启了"扫码后才读数"，则重量归零
    if (electronicScaleSettings.scanCodeReading) {
      formData.electronicWeight = 0
      formData.actualWeight = 0
    }
  }
}

const { fetchData: saveWeighingData, success: saveSuccess, msg: saveMsg } = WeighingFineCode()
// 称重保存
async function handleSave() {
  try {
    await formRef.value?.validate()

    if (!formData.fineCodeId) {
      ElMessage.error('请先扫码获取细码信息')
      return
    }

    if (!electronicScaleSettings.noWeaverSelection && !formData.weaverId) {
      ElMessage.error('请选择织工')
      return
    }

    loading.value = true

    const requestData: any = {
      id: formData.fineCodeId,
      actual_weight: formatWeightMul(formData.actualWeight),
      electronic_scale_weight: formatWeightMul(formData.electronicWeight),
      weaver_id: formData.weaverId,
      weaver_name: formData.weaverName,
      is_shift_weaver: formData.isShiftWorker,
    }

    // 如果是交班织工，添加交班相关信息
    if (formData.isShiftWorker) {
      if (formData.shiftDuration)
        requestData.shift_speed = Number(formData.shiftDuration)

      if (formData.shiftWeaverId)
        requestData.shift_weaver_id = formData.shiftWeaverId

      if (formData.shiftWeaverName)
        requestData.shift_weaver_name = formData.shiftWeaverName
    }

    await saveWeighingData(requestData)

    if (saveSuccess.value) {
      ElMessage.success('保存成功')
      handleSaveChar()
      // 重新获取细码详情以更新显示
      if (formData.fineCodeId)
        await getFineCodeDetail(undefined, formData.fineCodeId)
    }
    else {
      ElMessage.error(saveMsg.value || '保存失败')
    }
  }
  catch (error: any) {
    ElMessage.error(error.message || '保存失败，请检查表单填写')
  }
  finally {
    loading.value = false
  }
}

// 清空表单
function handleSaveChar() {
  // 重置表单数据
  Object.assign(formData, {
    barcode: '',
    machineNumber: '',
    workerName: '',
    isShiftWorker: false,
    shiftDuration: '',
    shiftWeaverId: null,
    shiftWeaverName: '',
    electronicWeight: 0,
    actualWeight: 0,
    fineCodeId: null,
  })

  // 重置生产详情数据
  Object.assign(productionDetails, {
    orderName: '',
    orderNumber: '',
    version: '',
    versionNumber: '',
    batch: '',
    machineType: '',
    machineColor: '',
    needleSize: '',
    group: '',
    machine: '',
    isShiftWorker: '',
    shiftTime: '',
    shiftWorker: '',
    shiftWeaverName: '',
    actualWeight: '',
    operator: '',
    monthlyOutput: '',
    yearlyOutput: '',
    productionNote: '',
    weightOfFabric: '',
  })
}

// 自动保存切换
function handleAutoSaveToggle() {
  if (autoSave.value)
    ElMessage.info('已开启自动保存')
  else
    ElMessage.info('已关闭自动保存')
}

// 连接电子秤串口设备
function handleConnectToSerialPort() {
  if (!isWeightConnected.value) {
    const delimiterConfig = {
      dataHeader: electronicScaleSettings.dataHead
        ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
        : [],
      dataFooter: electronicScaleSettings.dataEnd
        ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
        : [13, 10], // 默认 \r\n
      useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
    }

    const stabilityConfig = {
      enabled: !!electronicScaleSettings.stableValue, // 0 则disabled
      stableCount: Number(electronicScaleSettings.stableValue) || 0,
      precision: weightPrecision.value, // 默认精度到小数点后2位
    }

    const measureWeightService = useMeasureWeightService(delimiterConfig, stabilityConfig)
    try {
      measureWeightService.connectToSerialPort()
      measureWeightStore.setMeasureWeightState(measureWeightService)
    }
    catch (error) {
      console.error('串口连接错误:', error)
    }
  }
}

// 断开电子秤串口连接
function handleDisconnectToWeightSerialPort() {
  measureWeightStore.measureWeightState.clearLogMessages()
  measureWeightStore.clearLogList()
  measureWeightStore.measureWeightState.disconnectPort()
}

// 切换重量日志显示
function toggleWeightLog() {
  showWeightLog.value = !showWeightLog.value
}

// 打开设置电子秤弹窗
function openElectronicScaleModal() {
  showElectronicScaleModal.value = true
}

const { fetchData: saveConfig } = BatchUpdateGlobalConfigList()
// 保存设置到全局配置
async function saveSettingsToGlobalConfig() {
  const configsToSave = [
    { id: configIdMap.autoSaveSeconds, options: electronicScaleSettings.autoSaveSeconds.toString() },
    { id: configIdMap.noWeaverSelection, options: electronicScaleSettings.noWeaverSelection.toString() },
    { id: configIdMap.noInspectorSelection, options: electronicScaleSettings.noInspectorSelection.toString() },
    { id: configIdMap.weightReflection, options: electronicScaleSettings.weightReflection.toString() },
    { id: configIdMap.dataHead, options: electronicScaleSettings.dataHead },
    { id: configIdMap.dataEnd, options: electronicScaleSettings.dataEnd },
    { id: configIdMap.scanCodeReading, options: electronicScaleSettings.scanCodeReading.toString() },
    { id: configIdMap.stableValue, options: electronicScaleSettings.stableValue.toString() },
    { id: configIdMap.autoSave, options: electronicScaleSettings.autoSave.toString() },
  ]

  try {
    await saveConfig({
      update_global_config_list: configsToSave.map((item) => {
        return {
          id: item.id,
          options: item.options,
        }
      }),
    })
    ElMessage.success('设置保存成功')
  }
  catch (error) {
    ElMessage.error('设置保存失败')
    console.error('保存设置失败:', error)
  }
}
function updateWeightConfig() {
  // 更新数据头尾配置
  const delimiterConfig = {
    dataHeader: electronicScaleSettings.dataHead
      ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
      : [],
    dataFooter: electronicScaleSettings.dataEnd
      ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
      : [13, 10], // 默认 \r\n
    useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
  }

  // 更新稳定性配置
  const stabilityConfig = {
    enabled: electronicScaleSettings.isStable,
    stableCount: Number(electronicScaleSettings.stableValue) || 3,
    precision: weightPrecision.value, // 默认精度到小数点后2位
  }

  // 更新现有连接的配置
  if (measureWeightStore.measureWeightState.updateDelimiterConfig)
    measureWeightStore.measureWeightState.updateDelimiterConfig(delimiterConfig)

  if (measureWeightStore.measureWeightState.updateStabilityConfig)
    measureWeightStore.measureWeightState.updateStabilityConfig(stabilityConfig)
}
// 保存电子秤设置
async function saveElectronicScaleSettings(settings?: any) {
  // 如果传入了设置参数，更新本地设置
  if (settings)
    Object.assign(electronicScaleSettings, settings)

  // 保存到全局配置
  await saveSettingsToGlobalConfig()

  // 如果已经连接，更新现有连接的配置
  if (isWeightConnected.value && measureWeightStore.measureWeightState) {
    updateWeightConfig()

    ElMessage.success('电子秤设置已更新并应用到当前连接')
  }

  // 如果是从组件保存，不需要关闭弹窗（组件会自己关闭）
  if (!settings)
    showElectronicScaleModal.value = false

  ElMessage.success('电子秤设置保存成功')
}

// 监听F4快捷键
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'F4') {
    event.preventDefault()
    handleSave()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  // 加载全局配置
  getSettingsData()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // 清理自动保存定时器
  if (autoSaveTimer)
    clearTimeout(autoSaveTimer)
})

// 表单验证规则
const rules = computed(() => ({
  barcode: [{ required: true, message: '请输入条码编号', trigger: 'blur' }],
  weaverId: [{ required: !electronicScaleSettings.noWeaverSelection, message: '请选择织工姓名', trigger: 'change' }],
  electronicWeight: [
    { required: true, message: '请输入电子秤重量', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (Number(value) <= 0)
          callback(new Error('电子秤重量必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
  actualWeight: [
    { required: true, message: '请输入实际重量', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (Number(value) <= 0)
          callback(new Error('实际重量必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
}))
</script>

<template>
  <div class="production-entry-page">
    <div class="main-content">
      <!-- 左侧：生产信息录入 -->
      <div class="left-panel">
        <el-card>
          <h2 class="font-bold text-lg">
            生产信息录入
          </h2>
          <el-form
            ref="formRef"
            label-position="top"
            :model="formData"
            :rules="rules"
            size="large"
            class="production-form"
          >
            <!-- 条码编号 -->
            <el-form-item label="条码编号" prop="barcode">
              <div class="input-with-icon">
                <el-input
                  v-model="formData.barcode"
                  placeholder="扫码自动识别或手动输入"
                  clearable
                  @change="handleBarcodeInput"
                  @keyup.enter="handleBarcodeInput"
                >
                  <template #append>
                    <SvgIcon name="tiaoma2" size="20px" />
                  </template>
                </el-input>
              </div>
            </el-form-item>

            <!-- 机台号 -->
            <el-form-item label="机台号" prop="machineNumber">
              <div class="input-with-icon">
                <div class="flex items-center">
                  <span class="text-ls text-gray-500">
                    {{ formData.machineNumber || '扫条形码后自动录入机台号' }}
                  </span>
                  <!-- <el-icon class="input-icon">
                    <Setting />
                  </el-icon> -->
                </div>
                <!-- <el-input
                  v-model="formData.machineNumber"
                  size="default"
                  placeholder="自动带出机台号"
                  readonly
                >
                  <template #append>
                    <el-icon class="input-icon">
                      <Setting />
                    </el-icon>
                  </template>
                </el-input> -->
              </div>
            </el-form-item>

            <!-- 织工姓名 -->
            <el-form-item label="织工姓名" prop="weaverId">
              <div class="worker-input-group">
                <SelectMergeComponent
                  v-model="formData.weaverId"
                  :custom-label="(row:any) => `${formatHashTag(row.code, row.name)}`"
                  :query="{ duty: EmployeeType.follower }"
                  api-name="Adminemployeelist"
                  placeholder="请选择织工姓名"
                  remote
                  remote-key="code_or_name"
                  remote-show-suffix
                  label-field="name"
                  value-field="id"
                  clearable
                  @change="(item) => formData.weaverName = item.name"
                />
                <el-button class="ml-2" @click="() => formData.weaverId = null">
                  清空
                </el-button>
              </div>
            </el-form-item>
            <el-divider />

            <el-row class="w-full">
              <el-col :span="12">
                <!-- 是否为交班织工 -->
                <el-form-item label="是否为交班织工">
                  <el-radio-group
                    v-model="formData.isShiftWorker"
                  >
                    <el-radio :value="true">
                      是
                    </el-radio>
                    <el-radio :value="false">
                      否
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 是否为交班织工 -->
                <el-form-item label="交班织工">
                  <div class="flex">
                    <SelectMergeComponent
                      v-model="formData.shiftWeaverId"
                      :custom-label="(row:any) => `${formatHashTag(row.code, row.name)}`"
                      :query="{ duty: EmployeeType.follower }"
                      api-name="Adminemployeelist"
                      placeholder="请选择交班织工"
                      remote
                      remote-key="code_or_name"
                      remote-show-suffix
                      label-field="name"
                      value-field="id"
                      clearable
                      @change="(item) => formData.shiftWeaverName = item.name"
                    />
                    <el-button class="ml-2" @click="() => formData.shiftWeaverId = null">
                      清空
                    </el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 交班时长 -->
            <el-form-item label="交班转速(rpm)">
              <template #label>
                <div class="flex items-center">
                  <span>交班转速</span>
                </div>
              </template>
              <div class="input-with-icon">
                <el-input-number
                  v-model="formData.shiftDuration"
                  :precision="2"
                  :min="0"
                  placeholder="请输入交班转速"
                  class="shift-duration-input"
                >
                  <template #suffix>
                    <span>rpm</span>
                  </template>
                </el-input-number>
              </div>
            </el-form-item>
            <el-divider />

            <!-- 称重信息 -->
            <div class="weight-section">
              <h4 class="section-title">
                称重信息<el-tag type="primary" size="small" class="ml-2">
                  保留{{ DecimalPointLabels[weightPrecision] }}
                </el-tag>
                <el-tag type="primary" size="small" class="ml-2">
                  {{ CarryLabels[weightCarryType] }}
                </el-tag>
              </h4>
              <el-row class="w-full">
                <el-col :span="12">
                  <el-form-item label="" prop="electronicWeight">
                    <template #label>
                      <div class="weight-label-container ">
                        <span>电子秤重量</span>
                        <div v-if="electronicScaleSettings.scanCodeReading" class="scan-status-indicator">
                          <el-tooltip effect="dark" placement="top" :show-after="300">
                            <template #content>
                              <div class="custom-tooltip-content">
                                <div class="tooltip-title">
                                  扫码状态
                                </div>
                                <div class="tooltip-desc">
                                  {{ formData.barcode ? '✅ 已扫码，可以称重' : '⚠️ 请先扫码才能称重' }}
                                </div>
                              </div>
                            </template>
                            <div class="status-badge" :class="formData.barcode ? 'status-success' : 'status-warning'">
                              <el-icon class="status-icon">
                                <InfoFilled />
                              </el-icon>
                              <span class="status-text">{{ formData.barcode ? '已扫码' : '未扫码' }}</span>
                            </div>
                          </el-tooltip>
                        </div>
                      </div>
                    </template>
                    <div class="input-with-icon">
                      <el-input-number
                        v-model="formData.electronicWeight"
                        :precision="weightPrecision"
                        :min="0"
                        placeholder="电子秤重量"
                      >
                        <template #suffix>
                          <span>kg</span>
                        </template>
                      </el-input-number>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="" prop="actualWeight">
                    <template #label>
                      <span class="inline-flex items-center">
                        <span>实际重量</span>
                      </span>
                    </template>
                    <div class="actual-weight-group">
                      <div class="input-with-icon">
                        <el-input-number
                          v-model="formData.actualWeight"
                          :precision="weightPrecision"
                          :min="0"
                          placeholder="实际重量（稳定后重量）"
                        >
                          <template #suffix>
                            <span>kg</span>
                          </template>
                        </el-input-number>
                      </div>
                      <span class="weight-note">坯布信息布要求定重：{{ formatWeightDiv(productionDetails.weightOfFabric) || '0' }}kg</span>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
          <div class="action-buttons">
            <div class="left-actions">
              <el-checkbox v-model="autoSave" size="large" class="auto-save-checkbox" @change="handleAutoSaveToggle">
                <span class="checkbox-text">自动保存</span>
              </el-checkbox>
              <el-button size="large" class="settings-btn" @click="openElectronicScaleModal">
                <el-icon><Setting /></el-icon>
                设置电子秤
              </el-button>
            </div>
            <div class="right-actions">
              <el-button size="large" class="clear-btn" :disabled="loading" @click="handleSaveChar">
                <el-icon><RefreshLeft /></el-icon>
                清空
              </el-button>
              <el-button size="large" type="primary" class="save-btn" :loading="loading" @click="handleSave">
                <el-icon><Check /></el-icon>
                保存数据(F4)
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 中间：基本信息 -->
      <div class="right-panel">
        <el-card>
          <h2 class="font-bold text-lg">
            基本信息
          </h2>
          <div class="production-details">
            <!-- 基本信息 -->
            <div class="detail-row">
              <div class="detail-item">
                <label>订单名称：</label>
                <span class="detail-value">{{ productionDetails.orderName }}</span>
              </div>
              <div class="detail-item">
                <label>条码：</label>
                <span class="detail-value">{{ productionDetails.orderNumber }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>版号：</label>
                <span class="detail-value">{{ productionDetails.version }}</span>
              </div>
              <div class="detail-item">
                <label>卷号：</label>
                <span class="detail-value">{{ productionDetails.versionNumber }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>纱批：</label>
                <span class="detail-value">{{ productionDetails.batch }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>织机类型：</label>
                <span class="detail-value">{{ productionDetails.machineType }}</span>
              </div>
              <div class="detail-item">
                <label>原料颜色：</label>
                <span class="detail-value">{{ productionDetails.machineColor }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>针寸数：</label>
                <span class="detail-value">{{ productionDetails.needleSize }}</span>
              </div>
              <div class="detail-item">
                <label>班次：</label>
                <span class="detail-value">{{ productionDetails.group }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>机台：</label>
                <span class="detail-value">{{ productionDetails.machine }}</span>
              </div>
              <div class="detail-item">
                <label>是否交班布：</label>
                <span class="detail-value highlight">{{ productionDetails.isShiftWorker }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>称重：</label>
                <span class="detail-value">{{ productionDetails.shiftTime }}kg</span>
              </div>
              <div class="detail-item">
                <label>交班转速：</label>
                <span class="detail-value highlight">{{ productionDetails.shiftDuration }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>实重：</label>
                <span class="detail-value">{{ productionDetails.actualWeight || '0' }}kg</span>
              </div>
              <div class="detail-item">
                <label>交班织工：</label>
                <span class="detail-value highlight">{{ productionDetails.shiftWeaverName }}</span>
              </div>
            </div>

            <div class="detail-row">
              <div class="detail-item">
                <label>织工：</label>
                <span class="detail-value">{{ productionDetails.operator || '' }}</span>
              </div>
              <div class="detail-item flex">
                <div class="detail-item !p-0">
                  <label>本月产量：</label>
                  <span class="detail-value">{{ productionDetails.monthlyOutput }}</span>
                </div>
                <div class="detail-item !p-0">
                  <label>本月产量：</label>
                  <span class="detail-value">{{ productionDetails.yearlyOutput }}</span>
                </div>
              </div>
            </div>

            <!-- 生产备注 -->
            <div class="production-note">
              <label>生产备注：</label>
              <p class="note-content">
                {{ productionDetails.productionNote }}
              </p>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 设置电子秤弹窗 -->
    <ElectronicScaleSettings
      v-model="showElectronicScaleModal"
      :settings="electronicScaleSettings"
      :show-meter-connection="false"
      :show-inspector-option="false"
      :is-weight-connected="isWeightConnected"
      :show-weight-log="showWeightLog"
      @save="saveElectronicScaleSettings"
      @connect-weight="handleConnectToSerialPort"
      @disconnect-weight="handleDisconnectToWeightSerialPort"
      @toggle-weight-log="toggleWeightLog"
    >
      <template #weight-log>
        <WeightLog v-if="showWeightLog" />
      </template>
    </ElectronicScaleSettings>
  </div>
</template>

<style lang="scss" scoped>
.production-entry-page {
  background: #f8fafc;
  min-height: 100vh;
  padding: 16px;

  .main-content {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    .left-panel {
      flex: 1;
      min-width: 400px;

      :deep(.el-card) {
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: none;
        overflow: visible;

        .el-card__body {
          padding: 20px;
        }
      }
    }

    .right-panel {
      flex: 1;
      min-width: 400px;

      :deep(.el-card) {
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: none;
        overflow: visible;

        .el-card__body {
          padding: 20px;
        }
      }
    }
  }

  // 页面标题样式
  h2 {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 20px;
    padding: 16px 20px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  // 生产信息录入表单样式
  .production-form {
    .input-with-icon {
      width: 100%;

      :deep(.el-input) {
        .el-input__wrapper {
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
          }

          &.is-focus {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        }
      }
    }

    .worker-input-group {
      display: flex;
      gap: 12px;

      :deep(.el-select) {
        .el-input__wrapper {
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
          }

          &.is-focus {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        }
      }
    }

    .shift-duration-input {
      margin-bottom: 8px;

      :deep(.el-input-number) {
        width: 100%;

        .el-input__wrapper {
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
          }

          &.is-focus {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        }
      }
    }

    // 分组标题样式
    .weight-section {
      margin-top: 20px;

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 8px 12px;
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-left: 4px solid #f59e0b;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        color: #92400e;

        .el-tag {
          margin-left: 8px;
        }
      }

      .actual-weight-group {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .weight-note {
          font-size: 12px;
          color: #909399;
          line-height: 1.2;
        }
      }
    }

    // 单选按钮组样式
    :deep(.el-radio-group) {
      .el-radio {
        margin-right: 20px;

        .el-radio__label {
          font-weight: 500;
        }
      }
    }

    // 分割线样式
    :deep(.el-divider) {
      margin: 24px 0;
      border-color: #e5e7eb;
    }
  }

  // 生产参数详情样式
  .production-details {
    .detail-row {
      display: flex;
      margin-bottom: 12px;
      gap: 12px;

      .detail-item {
        flex: 1;
        min-height: 32px;
        background: #ffffff;
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
          transform: translateY(-1px);
        }

        label {
          display: block;
          font-weight: 500;
          color: #6b7280;
          margin-bottom: 4px;
          white-space: nowrap;
          min-width: 70px;
          font-size: 12px;
        }

        .detail-value {
          color: #111827;
          font-weight: 600;
          word-break: break-all;
          font-size: 14px;

          &.highlight {
            color: #059669;
            background: #ecfdf5;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
          }
        }
      }
    }

    .production-note {
      padding: 16px;
      background: #f9fafb;
      border: 1px dashed #d1d5db;
      border-radius: 8px;
      margin-top: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #409eff;

      label {
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
        display: block;
      }

      .note-content {
        margin: 0;
        line-height: 1.6;
        color: #606266;
        font-size: 14px;
      }
    }
  }

}
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 20px;
  background: #fafbfc;
  border-radius: 0 0 8px 8px;

  .left-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .auto-save-checkbox {
      .checkbox-text {
        color: #64748b;
        font-weight: 500;
      }
    }

    .settings-btn {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      color: #64748b;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
        color: #475569;
        transform: translateY(-1px);
      }
    }
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .clear-btn {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      color: #64748b;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
        color: #475569;
        transform: translateY(-1px);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }
    }

    .save-btn {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border: none;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(16, 185, 129, 0.25);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(16, 185, 129, 0.35);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
}

// 美化提示样式
.weight-label-container {
  display: inline-flex;
  align-items: center;
  gap: 8px;

  .scan-status-indicator {
    .status-badge {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.3s ease;
      cursor: pointer;

      &.status-success {
        background: linear-gradient(135deg, #67c23a, #85ce61);
        color: white;
        box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(103, 194, 58, 0.4);
        }
      }

      &.status-warning {
        background: linear-gradient(135deg, #e6a23c, #ebb563);
        color: white;
        box-shadow: 0 2px 4px rgba(230, 162, 60, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(230, 162, 60, 0.4);
        }
      }

      .status-icon {
        font-size: 12px;
      }

      .status-text {
        font-size: 11px;
        white-space: nowrap;
      }
    }
  }
}

.setting-label-container {
  display: flex;
  align-items: center;
  gap: 8px;

  .info-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #409eff, #66b1ff);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
    }

    .info-icon {
      color: white;
      font-size: 12px;
    }
  }
}

.custom-tooltip-content {
  .tooltip-title {
    font-weight: 600;
    font-size: 14px;
    color: #fff;
    margin-bottom: 6px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 4px;
  }

  .tooltip-desc {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
  }
}

.stable-value-container {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .input-group {
    .stable-input {
      width: 200px;

      .input-suffix {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .setting-hint {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #bae6fd;
    border-radius: 6px;

    .hint-icon {
      color: #0ea5e9;
      font-size: 14px;
      margin-top: 1px;
      flex-shrink: 0;
    }

    .hint-text {
      font-size: 12px;
      color: #0369a1;
      line-height: 1.4;
    }
  }
}

.data-setting-container {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .data-input {
    width: 300px;
  }

  .setting-note {
    font-size: 12px;
    color: #909399;
    padding-left: 4px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .production-entry-page {
    .main-content {
      flex-direction: column;

      .left-panel,
      .right-panel {
        min-width: auto;
      }
    }
  }

  .weight-label-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .data-setting-container {
    .data-input {
      width: 100%;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .production-entry-page {
    padding: 12px;

    .main-content {
      flex-direction: column;
      gap: 12px;

      .left-panel, .right-panel {
        min-width: auto;
        width: 100%;
        flex: none;
      }
    }
  }
}

@media (max-width: 768px) {
  .production-entry-page {
    padding: 8px;

    .main-content {
      gap: 8px;

      .left-panel, .right-panel {
        :deep(.el-card) {
          .el-card__body {
            padding: 16px;
          }
        }
      }
    }
  }
}

// 全局样式调整
:deep(.el-input-number) {
  width: 100%;

  .el-input__inner {
    text-align: left;
  }
}
</style>
