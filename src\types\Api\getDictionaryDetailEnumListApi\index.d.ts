declare namespace Api.GetDictionaryDetailEnumListApi {
  export interface Request {
    /**
     * 编号或名称
     */
    code_or_name?: string
    /**
     * 字典id
     */
    dictionary_id?: number
    /**
     * download
     */
    download?: number
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    /**
     * 状态
     */
    status?: number
    [property: string]: any
  }
  /**
   * system.GetDictionaryDetailEnumData
   */
  export interface Response {
    code?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    default?: boolean
    id?: number
    name?: string
    remark?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
